<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .resource-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .resource-item {
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 资源加载测试工具</h1>
    <p>此工具用于测试聊天应用的资源加载和回退机制</p>

    <div class="test-section">
        <h2>📊 资源加载状态</h2>
        <div id="resourceStatus" class="resource-list">
            <div class="resource-item loading">检查中...</div>
        </div>
        <button onclick="checkResources()">🔄 重新检查</button>
        <button onclick="testFallback()">🧪 测试回退机制</button>
    </div>

    <div class="test-section">
        <h2>🌐 网络连接测试</h2>
        <div id="networkStatus" class="status loading">正在测试网络连接...</div>
        <button onclick="testNetwork()">🔄 测试网络</button>
    </div>

    <div class="test-section">
        <h2>⚡ 性能测试</h2>
        <div id="performanceStatus" class="status loading">准备测试...</div>
        <button onclick="testPerformance()">🚀 开始性能测试</button>
    </div>

    <div class="test-section">
        <h2>📝 测试日志</h2>
        <div id="testLog" class="log">等待测试开始...\n</div>
        <button onclick="clearLog()">🗑️ 清空日志</button>
        <button onclick="exportLog()">📄 导出日志</button>
    </div>

    <script>
        // 日志记录
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const prefix = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 检查资源加载状态
        function checkResources() {
            log('开始检查资源加载状态...');
            const container = document.getElementById('resourceStatus');
            container.innerHTML = '';

            const resources = [
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined', cdn: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js' },
                { name: 'Marked.js', check: () => typeof marked !== 'undefined', cdn: 'https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js' },
                { name: 'KaTeX', check: () => typeof katex !== 'undefined', cdn: 'https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js' },
                { name: 'Highlight.js', check: () => typeof hljs !== 'undefined', cdn: 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/highlight.min.js' },
                { name: 'Mermaid', check: () => typeof mermaid !== 'undefined', cdn: 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js' }
            ];

            resources.forEach(resource => {
                const item = document.createElement('div');
                item.className = 'resource-item';
                
                if (resource.check()) {
                    item.classList.add('success');
                    item.textContent = `✅ ${resource.name}`;
                    log(`${resource.name} 已加载`);
                } else {
                    item.classList.add('error');
                    item.textContent = `❌ ${resource.name}`;
                    log(`${resource.name} 未加载`, 'error');
                }
                
                container.appendChild(item);
            });
        }

        // 测试回退机制
        function testFallback() {
            log('开始测试回退机制...');
            
            // 模拟CDN加载失败
            const testScript = document.createElement('script');
            testScript.src = 'https://nonexistent-cdn.example.com/test.js';
            
            const startTime = Date.now();
            let timeoutTriggered = false;
            
            // 设置超时
            const timeout = setTimeout(() => {
                timeoutTriggered = true;
                const loadTime = Date.now() - startTime;
                log(`CDN超时测试完成，耗时: ${loadTime}ms`, 'warning');
                
                // 移除失败的脚本
                if (testScript.parentNode) {
                    testScript.parentNode.removeChild(testScript);
                }
                
                // 测试本地回退
                log('测试本地资源回退...');
                const fallbackScript = document.createElement('script');
                fallbackScript.textContent = 'window.testFallbackSuccess = true;';
                document.head.appendChild(fallbackScript);
                
                setTimeout(() => {
                    if (window.testFallbackSuccess) {
                        log('本地回退机制测试成功', 'success');
                    } else {
                        log('本地回退机制测试失败', 'error');
                    }
                }, 100);
            }, 5000);
            
            testScript.onload = () => {
                if (!timeoutTriggered) {
                    clearTimeout(timeout);
                    log('意外成功：测试CDN实际可用', 'warning');
                }
            };
            
            testScript.onerror = () => {
                if (!timeoutTriggered) {
                    clearTimeout(timeout);
                    const loadTime = Date.now() - startTime;
                    log(`CDN错误测试完成，耗时: ${loadTime}ms`, 'success');
                }
            };
            
            document.head.appendChild(testScript);
        }

        // 测试网络连接
        async function testNetwork() {
            log('开始测试网络连接...');
            const statusElement = document.getElementById('networkStatus');
            statusElement.className = 'status loading';
            statusElement.textContent = '正在测试网络连接...';

            const testUrls = [
                'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.11.1/highlight.min.js',
                'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap'
            ];

            let successCount = 0;
            let totalTime = 0;

            for (const url of testUrls) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' });
                    const loadTime = Date.now() - startTime;
                    totalTime += loadTime;
                    successCount++;
                    log(`✅ ${url.split('/').pop()} - ${loadTime}ms`);
                } catch (error) {
                    log(`❌ ${url.split('/').pop()} - 失败: ${error.message}`, 'error');
                }
            }

            const avgTime = totalTime / successCount;
            if (successCount === testUrls.length) {
                statusElement.className = 'status success';
                statusElement.textContent = `✅ 网络连接良好 (平均响应时间: ${avgTime.toFixed(0)}ms)`;
                log(`网络测试完成：${successCount}/${testUrls.length} 成功`, 'success');
            } else {
                statusElement.className = 'status warning';
                statusElement.textContent = `⚠️ 网络连接不稳定 (${successCount}/${testUrls.length} 成功)`;
                log(`网络测试完成：${successCount}/${testUrls.length} 成功`, 'warning');
            }
        }

        // 性能测试
        function testPerformance() {
            log('开始性能测试...');
            const statusElement = document.getElementById('performanceStatus');
            statusElement.className = 'status loading';
            statusElement.textContent = '正在进行性能测试...';

            // 测试DOM操作性能
            const startTime = performance.now();
            
            // 创建大量DOM元素
            const testContainer = document.createElement('div');
            for (let i = 0; i < 1000; i++) {
                const element = document.createElement('div');
                element.textContent = `测试元素 ${i}`;
                testContainer.appendChild(element);
            }
            
            const domTime = performance.now() - startTime;
            log(`DOM操作性能: ${domTime.toFixed(2)}ms (1000个元素)`);

            // 测试内存使用
            if (performance.memory) {
                const memory = performance.memory;
                log(`内存使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB / ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
            }

            // 测试localStorage性能
            const localStorageStart = performance.now();
            for (let i = 0; i < 100; i++) {
                localStorage.setItem(`test_${i}`, JSON.stringify({ data: `测试数据${i}` }));
            }
            const localStorageTime = performance.now() - localStorageStart;
            log(`localStorage性能: ${localStorageTime.toFixed(2)}ms (100次写入)`);

            // 清理测试数据
            for (let i = 0; i < 100; i++) {
                localStorage.removeItem(`test_${i}`);
            }

            statusElement.className = 'status success';
            statusElement.textContent = `✅ 性能测试完成 (DOM: ${domTime.toFixed(0)}ms, Storage: ${localStorageTime.toFixed(0)}ms)`;
            log('性能测试完成', 'success');
        }

        // 清空日志
        function clearLog() {
            document.getElementById('testLog').textContent = '';
            log('日志已清空');
        }

        // 导出日志
        function exportLog() {
            const logContent = document.getElementById('testLog').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `resource_test_log_${new Date().toISOString().split('T')[0]}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            log('日志已导出');
        }

        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动检查...');
            setTimeout(checkResources, 1000);
            setTimeout(testNetwork, 2000);
        });
    </script>
</body>
</html>

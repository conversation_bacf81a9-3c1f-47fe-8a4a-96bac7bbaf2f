// 测试修复效果的脚本

console.log('🔧 开始测试修复效果...');

// 1. 测试代码高亮修复
function testCodeHighlightFix() {
    console.log('📝 测试代码高亮修复...');
    
    // 检查 highlight.js 是否加载
    if (typeof hljs === 'undefined') {
        console.error('❌ highlight.js 未加载');
        return false;
    }
    
    // 创建测试代码块
    const testContainer = document.createElement('div');
    testContainer.innerHTML = `
        <pre><code class="language-javascript">
function test() {
    console.log("Hello World");
    return true;
}
        </code></pre>
    `;
    
    document.body.appendChild(testContainer);
    
    // 测试应用高亮
    try {
        if (window.chatApp && typeof window.chatApp.applyHighlightingToAllCodeBlocks === 'function') {
            window.chatApp.applyHighlightingToAllCodeBlocks(testContainer);
            
            // 检查是否应用了高亮
            const codeElement = testContainer.querySelector('code');
            const hasHighlight = codeElement.classList.contains('hljs') || codeElement.innerHTML.includes('<span');
            
            if (hasHighlight) {
                console.log('✅ 代码高亮修复成功');
                document.body.removeChild(testContainer);
                return true;
            } else {
                console.warn('⚠️ 代码高亮可能未正确应用');
                document.body.removeChild(testContainer);
                return false;
            }
        } else {
            console.error('❌ applyHighlightingToAllCodeBlocks 方法不存在');
            document.body.removeChild(testContainer);
            return false;
        }
    } catch (error) {
        console.error('❌ 代码高亮测试失败:', error);
        document.body.removeChild(testContainer);
        return false;
    }
}

// 2. 测试模型头像修复
function testModelAvatarFix() {
    console.log('🖼️ 测试模型头像修复...');
    
    // 创建测试头像元素
    const testImg = document.createElement('img');
    testImg.src = 'https://example.com/nonexistent-avatar.png'; // 故意使用不存在的URL
    testImg.alt = 'Test Avatar';
    
    // 应用修复后的错误处理
    testImg.onerror = function() {
        this.style.display = 'none';
        this.parentElement.innerHTML += '<div style="width: 36px; height: 36px; border-radius: 50%; background: var(--bg-tertiary); display: flex; align-items: center; justify-content: center; color: var(--text-tertiary); font-size: 18px;">🤖</div>';
        console.log('✅ 模型头像错误处理修复成功');
    };
    
    const testContainer = document.createElement('div');
    testContainer.appendChild(testImg);
    document.body.appendChild(testContainer);
    
    // 模拟加载失败
    setTimeout(() => {
        testImg.dispatchEvent(new Event('error'));
        setTimeout(() => {
            document.body.removeChild(testContainer);
        }, 100);
    }, 100);
    
    return true;
}

// 3. 测试流式输出结束时的最终渲染
function testStreamingFinalRender() {
    console.log('🔄 测试流式输出最终渲染...');
    
    if (!window.chatApp) {
        console.error('❌ ChatApp 实例不存在');
        return false;
    }
    
    // 检查 stopTyping 方法是否存在
    if (typeof window.chatApp.stopTyping === 'function') {
        console.log('✅ stopTyping 方法存在');
        
        // 检查 updateMessageContentWithMath 方法是否存在
        if (typeof window.chatApp.updateMessageContentWithMath === 'function') {
            console.log('✅ updateMessageContentWithMath 方法存在');
            
            // 检查 applyHighlightingToAllCodeBlocks 方法是否存在
            if (typeof window.chatApp.applyHighlightingToAllCodeBlocks === 'function') {
                console.log('✅ applyHighlightingToAllCodeBlocks 方法存在');
                console.log('✅ 流式输出最终渲染修复应该有效');
                return true;
            } else {
                console.error('❌ applyHighlightingToAllCodeBlocks 方法不存在');
                return false;
            }
        } else {
            console.error('❌ updateMessageContentWithMath 方法不存在');
            return false;
        }
    } else {
        console.error('❌ stopTyping 方法不存在');
        return false;
    }
}

// 4. 检查默认头像SVG是否正确
function testDefaultAvatarSVG() {
    console.log('🎨 测试默认头像SVG...');
    
    const defaultAvatarSVG = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNmMGYwZjAiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNEMxNCA1LjEgMTMuMSA2IDEyIDZDMTAuOSA2IDEwIDUuMSAxMCA0QzEwIDIuOSAxMC45IDIgMTIgMlpNMjEgOVYyMkMyMSAyMi41IDIwLjUgMjMgMjAgMjNIMTlWMTZIMTNWMjNIMTJDMTEuNSAyMyAxMSAyMi41IDExIDIyVjlDMTEgOC41IDExLjUgOCAxMiA4SDE0VjEwSDEwVjEySDEyVjE0SDE0VjEySDIwQzIwLjUgMTIgMjEgMTIuNSAyMSAxM1Y5WiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4KPC9zdmc+';
    
    // 创建测试图片
    const testImg = document.createElement('img');
    testImg.src = defaultAvatarSVG;
    testImg.style.width = '40px';
    testImg.style.height = '40px';
    
    testImg.onload = function() {
        console.log('✅ 默认头像SVG加载成功');
    };
    
    testImg.onerror = function() {
        console.error('❌ 默认头像SVG加载失败');
    };
    
    document.body.appendChild(testImg);
    setTimeout(() => {
        document.body.removeChild(testImg);
    }, 1000);
    
    return true;
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行所有修复测试...');
    
    const results = {
        codeHighlight: false,
        modelAvatar: false,
        streamingRender: false,
        defaultAvatar: false
    };
    
    // 延迟执行测试，确保页面完全加载
    setTimeout(() => {
        results.codeHighlight = testCodeHighlightFix();
        results.modelAvatar = testModelAvatarFix();
        results.streamingRender = testStreamingFinalRender();
        results.defaultAvatar = testDefaultAvatarSVG();
        
        // 输出测试结果
        setTimeout(() => {
            console.log('📊 测试结果汇总:');
            console.log('  代码高亮修复:', results.codeHighlight ? '✅ 通过' : '❌ 失败');
            console.log('  模型头像修复:', results.modelAvatar ? '✅ 通过' : '❌ 失败');
            console.log('  流式渲染修复:', results.streamingRender ? '✅ 通过' : '❌ 失败');
            console.log('  默认头像SVG:', results.defaultAvatar ? '✅ 通过' : '❌ 失败');
            
            const passedTests = Object.values(results).filter(Boolean).length;
            const totalTests = Object.keys(results).length;
            
            console.log(`🎯 总体结果: ${passedTests}/${totalTests} 项测试通过`);
            
            if (passedTests === totalTests) {
                console.log('🎉 所有修复都已成功应用！');
            } else {
                console.warn('⚠️ 部分修复可能需要进一步调整');
            }
        }, 500);
    }, 1000);
}

// 导出测试函数
window.testFixes = {
    runAll: runAllTests,
    codeHighlight: testCodeHighlightFix,
    modelAvatar: testModelAvatarFix,
    streamingRender: testStreamingFinalRender,
    defaultAvatar: testDefaultAvatarSVG
};

console.log('🔧 修复测试脚本已加载');
console.log('📋 可用的测试命令:');
console.log('  - testFixes.runAll() - 运行所有测试');
console.log('  - testFixes.codeHighlight() - 测试代码高亮修复');
console.log('  - testFixes.modelAvatar() - 测试模型头像修复');
console.log('  - testFixes.streamingRender() - 测试流式渲染修复');
console.log('  - testFixes.defaultAvatar() - 测试默认头像SVG');

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined' && window.document) {
    // 等待页面加载完成后自动运行测试
    if (document.readyState === 'complete') {
        runAllTests();
    } else {
        window.addEventListener('load', runAllTests);
    }
}
